import {AxiosError} from 'axios';
import {OrderDataType, OrderResponseDataType} from '../../types/orders';
import {castToOrderType} from '../../utils/types-casting/order';
import {GET} from '../../../../lib/http-methods';
import {PaginationType} from '../../../../types';
import extractJWTokens from '../../../auth/utils/jwt/extract-tokens';
import {refreshToken} from '../../../auth/services/refresh-token';

export async function retrieveUserOrders(
  page: number,
  limit: number,
): Promise<{
  orders: OrderDataType[];
  pagination: PaginationType;
} | null> {
  const {access} = await extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET(
      `${process.env.BACKEND_ADDRESS}/orders?page=${page}&limit=${limit}`,
      header,
    );
    return {
      pagination: res.data.pagination as PaginationType,
      orders: (res.data.data as OrderResponseDataType[]).map(
        (orderInResponse) => castToOrderType(orderInResponse),
      ),
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => retrieveUserOrders(page, limit));

      //unauthorized user error is already handled by the user hook
      if (!res)
        return {
          orders: [],
          pagination: {
            records: 1,
            currentPage: 1,
            totalPages: 1,
          },
        };
      return res;
    }

    return {
      orders: [],
      pagination: {
        records: 1,
        currentPage: 1,
        totalPages: 1,
      },
    };
  }
}
