import {keepPreviousData, useQuery} from '@tanstack/react-query';
import useUserStore from '../store/user-store';
import {useEffect} from 'react';
import useAuthRefresher from '../context/auth-refresher';
import {retrieveUserDetails} from '../services/user-details-extraction';

export default function useUser() {
  const {authRefresher} = useAuthRefresher();
  const {data, isLoading} = useQuery({
    queryKey: ['user-data', authRefresher],
    queryFn: () => retrieveUserDetails(),
    placeholderData: keepPreviousData,
  });
  const {
    setUser,
    setIsLoading: setUserIsLoading,
    user,
  } = useUserStore((store) => store);

  useEffect(() => {
    if (data) setUser(data);

    return () => {
      setUser(null);
    };
  }, [data, isLoading]);

  useEffect(() => {
    setUserIsLoading(isLoading);
  }, [isLoading]);

  return {
    user: data,
    isLoading,
  };
}
