import { loadCountries, loadCountry } from "@shopify/address";
import { useEffect, useState } from "react";
import { useCheckoutStore } from "../../store/checkout-store";

export default function useCountries() {
  const [countries, setCountries] = useState<{ code: string; name: string }[]>(
    []
  );
  const { country, setCountry } = useCheckoutStore((store) => store); //used to display the default choosed country
  const { countryOptionnalLabels, setCountryOptionnalLabels } =
    useCheckoutStore();

  useEffect(() => {
    loadCountries("en-US")
      .then((countries) => {
        setCountries(
          countries.map((country) => {
            return {
              code: country.code,
              name: country.name,
            };
          })
        );
      })
      .catch(() => {
        setCountries([{ code: "TN", name: "Tunisia" }]);
      });

    changeCountry("TN");
  }, []);

  function changeCountry(countryCode: string) {
    loadCountry("en-US", countryCode)
      .then((countryInfo) => {
        setCountry(countryInfo);

        //addition of optionnal fields if the country is libya
        const optionnalFields = Object.keys(countryInfo.optionalLabels);

        if (countryInfo?.code === "TN") {
          optionnalFields.push("zip");
          optionnalFields.push("company");
        }
        setCountryOptionnalLabels(optionnalFields);
      })
      .catch(() => {
        setCountry({
          name: "Tunisia",
          code: "TN",
          continent: "Africa",
          phoneNumberPrefix: 216,
          autocompletionField: "address1",
          provinceKey: "REGION",
          labels: {
            address1: "Address",
            address2: "Apartment, suite, etc",
            city: "City",
            company: "Company",
            country: "Country/region",
            firstName: "First name",
            lastName: "Last name",
            phone: "Phone",
            postalCode: "Postal code",
            zone: "Region",
          },
          optionalLabels: {
            address2: "Apartment, suite, etc (optional)",
          },
          formatting: {
            edit: "{country}_{firstName}{lastName}_{company}_{address1}_{address2}_{zip}{city}_{phone}",
            show: "{firstName} {lastName}_{company}_{address1}_{address2}_{zip} {city}_{country}_{phone}",
          },
          zones: [],
        });

        //addition of optionnal fields if the country is libya
        const optionnalFields = ["address2"];

        optionnalFields.push("zip");
        optionnalFields.push("company");

        setCountryOptionnalLabels(optionnalFields);
      });
  }

  return {
    country,
    countries,
    changeCountry,
    countryOptionnalLabels,
  };
}
