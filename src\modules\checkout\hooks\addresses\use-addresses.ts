import {useQuery} from '@tanstack/react-query';
import {retrieveUserAddresses} from '../../services/addresses/addresses-extraction';
import {getCurrentRoute, useAppNavigation} from '../../../../hooks';

export default function useAddresses() {
  const navigation = useAppNavigation();
  const pathname = getCurrentRoute(navigation.getState());
  const {data, isLoading} = useQuery({
    queryKey: ['user-address'],
    queryFn: () => retrieveUserAddresses(),
    enabled: ![
      'mon-compte',
      'mon-compte/commandes',
      'mon-compte/settings',
    ].every((disabledPathname) => pathname.endsWith(disabledPathname)), //data in those pages should not be fetched
  });

  return {
    addresses: data ? data : null,
    addressesAreLoading: isLoading,
  };
}
