import React from 'react';
import {ScrollView, View} from 'react-native';

import {useAppSelector} from '../../hooks';

import _v1 from './versions/_v1';

import {theme} from '../../constants';
import {useAppNavigation} from '../../hooks';
import {components} from '../../components';
import {text} from '../../text';
import useUserOrders from '../../modules/checkout/hooks/use-orders';

const OrderHistory: React.FC = (): JSX.Element => {
  const navigation = useAppNavigation();
  const version = useAppSelector((state) => state.appState.orderHistoryVersion);
  const {orders, ordersAreLoading, pagesNumber, currentPage, setPage} =
    useUserOrders(10);

  const renderStatusBar = (): JSX.Element => {
    return <components.StatusBar />;
  };

  const renderHomeIndicator = (): JSX.Element => {
    return <components.HomeIndicator />;
  };

  const renderHeader: () => JSX.Element = () => {
    return <components.Header goBack={true} title='Order history' />;
  };

  const renderContent: () => JSX.Element = () => {
    if (ordersAreLoading) {
      return <components.Loader />;
    }

    if (!orders || orders.length === 0) {
      return (
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
          }}
        >
          <text.H4 style={{marginBottom: 10, textAlign: 'center'}}>
            No orders found
          </text.H4>
          <text.T16
            style={{textAlign: 'center', color: theme.colors.textColor}}
          >
            You haven't placed any orders yet
          </text.T16>
        </View>
      );
    }

    return (
      <ScrollView
        contentContainerStyle={{
          paddingTop: 15,
          paddingHorizontal: 20,
          flexGrow: 1,
        }}
      >
        {version === 1 ? (
          orders.map((order) => (
            <components.OrderContainer
              key={order.id}
              order={order}
              onPress={() => navigation.navigate('TrackYourOrder')}
            />
          ))
        ) : (
          <_v1 />
        )}

        {/* Pagination Controls */}
        {pagesNumber > 1 && (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              paddingVertical: 20,
              gap: 10,
            }}
          >
            <TouchableOpacity
              onPress={() => setPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
                backgroundColor:
                  currentPage === 1
                    ? theme.colors.lightBlue
                    : theme.colors.mainColor,
                borderRadius: 4,
              }}
            >
              <text.T14
                style={{
                  color:
                    currentPage === 1
                      ? theme.colors.textColor
                      : theme.colors.white,
                }}
              >
                Previous
              </text.T14>
            </TouchableOpacity>

            <text.T14 style={{color: theme.colors.textColor}}>
              Page {currentPage} of {pagesNumber}
            </text.T14>

            <TouchableOpacity
              onPress={() => setPage(Math.min(pagesNumber, currentPage + 1))}
              disabled={currentPage === pagesNumber}
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
                backgroundColor:
                  currentPage === pagesNumber
                    ? theme.colors.lightBlue
                    : theme.colors.mainColor,
                borderRadius: 4,
              }}
            >
              <text.T14
                style={{
                  color:
                    currentPage === pagesNumber
                      ? theme.colors.textColor
                      : theme.colors.white,
                }}
              >
                Next
              </text.T14>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    );
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default OrderHistory;
