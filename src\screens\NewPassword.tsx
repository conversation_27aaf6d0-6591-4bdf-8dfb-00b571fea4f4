import React, {useState} from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {text} from '../text';
import {components} from '../components';
import {useAppNavigation} from '../hooks';
import useResetPassword from '../modules/auth/hooks/use-reset-password';
import {resetPassword} from '../modules/auth/services/reset-password/password-submition';
import {getServerErrorWarning} from '../modules/auth/utils/warnings/general-warning';

const NewPassword: React.FC = (): JSX.Element => {
  const navigation = useAppNavigation();
  const {email, code, setStep} = useResetPassword();

  // Local state for password fields and validation
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [passwordWarning, setPasswordWarning] = useState({
    password: '',
    confirmationPassword: '',
    generalWarning: '',
  });

  const handleSubmitPassword = async () => {
    // Reset warnings
    setPasswordWarning({
      password: '',
      confirmationPassword: '',
      generalWarning: '',
    });

    // Basic validation
    if (!password) {
      setPasswordWarning((prev) => ({
        ...prev,
        password: 'Password is required',
      }));
      return;
    }

    if (password.length < 6) {
      setPasswordWarning((prev) => ({
        ...prev,
        password: 'Password must be at least 6 characters',
      }));
      return;
    }

    if (password !== confirmPassword) {
      setPasswordWarning((prev) => ({
        ...prev,
        confirmationPassword: 'Passwords do not match',
      }));
      return;
    }

    setIsLoading(true);

    try {
      const res = await resetPassword({
        email,
        code,
        password,
      });

      setIsLoading(false);

      if (res.ok) {
        // Navigate to success screen or sign in
        navigation.navigate('SignIn');
      } else {
        setPasswordWarning({
          password: '',
          confirmationPassword: '',
          generalWarning: getServerErrorWarning(res.status),
        });
      }
    } catch (error) {
      setIsLoading(false);
      setPasswordWarning({
        password: '',
        confirmationPassword: '',
        generalWarning: 'An unexpected error occurred',
      });
    }
  };

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header title='Reset password' goBack={true} />;
  };

  const renderContent = () => {
    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingTop: 25,
          paddingBottom: 20,
        }}
      >
        <text.T16
          style={{
            marginBottom: 40,
          }}
        >
          Enter new password and confirm.
        </text.T16>
        <components.InputField
          label='password'
          placeholder='••••••••'
          onChangeText={(text) => setPassword(text)}
          containerStyle={{
            marginBottom: passwordWarning.password ? 40 : 20,
          }}
          value={password}
          eyeOffIcon={true}
          secureTextEntry={true}
          warning={passwordWarning.password}
        />
        <components.InputField
          label='confirm password'
          placeholder='••••••••'
          onChangeText={(text) => setConfirmPassword(text)}
          containerStyle={{
            marginBottom: passwordWarning.confirmationPassword ? 40 : 20,
          }}
          value={confirmPassword}
          eyeOffIcon={true}
          secureTextEntry={true}
          warning={passwordWarning.confirmationPassword}
        />
        {passwordWarning.generalWarning ? (
          <text.T14
            style={{color: '#E82837', marginBottom: 20, textAlign: 'center'}}
          >
            {passwordWarning.generalWarning}
          </text.T14>
        ) : null}
        <components.Button
          title={isLoading ? 'Changing password...' : 'Change password'}
          onPress={handleSubmitPassword}
        />
      </KeyboardAwareScrollView>
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default NewPassword;
