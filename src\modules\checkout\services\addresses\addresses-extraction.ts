import {AxiosError} from 'axios';

import {castToAddressType} from '../../utils/types-casting/addresses';
import extractJWTokens from '../../../auth/utils/jwt/extract-tokens';
import {AddressResponseDataType, AddressType} from '../../types/addresses';
import {GET} from '../../../../lib/http-methods';
import {refreshToken} from '../../../auth/services/refresh-token';

export async function retrieveUserAddresses(): Promise<AddressType[] | null> {
  const {access} = await extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET(`${process.env.BACKEND_ADDRESS}/addresses`, header);

    return (res.data as AddressResponseDataType[]).map((address) =>
      castToAddressType(address),
    );
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrieveUserAddresses);

      //unauthorized user error is already handled by the user hook
      if (!res) return [];
      return res;
    }

    return [];
  }
}
