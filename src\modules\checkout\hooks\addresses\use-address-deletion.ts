import { notFound } from "next/navigation";
import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import deleteAddressOnServerSide from "../../services/addresses/address-deletion";
import { useTranslations } from "next-intl";

export default function useAddressDeletion() {
  const queryClient = useQueryClient();
  const [deletionIsLoading, setDeletionIsLoading] = useState(false);
  const [deletionPopUpIsOpen, setDeletionPopUpIsOpen] = useState(false);
  const [addressId, setAddressId] = useState("");
  const t = useTranslations("shared.warnings");
  const [warning, setWarning] = useState("");

  function deleteAddress(addressId: string) {
    setAddressId(addressId);
    setDeletionPopUpIsOpen(true);
  }

  function cancelDeletion() {
    setDeletionPopUpIsOpen(false);
    setAddressId("");
  }

  function confirmAddressDeletion() {
    setDeletionIsLoading(true);

    if (warning !== "") setWarning("");

    deleteAddressOnServerSide(addressId).then((res) => {
      if (!res.ok) {
        if (res.status === 401)
          //unauthorized user
          notFound();
        else {
          //display error in toast
          setWarning(t("serverError"));
        }
      } else {
        queryClient.invalidateQueries({ queryKey: ["user-address"] });
        cancelDeletion();
      }

      //stop button loader and close pop up and remove address id
      setDeletionIsLoading(false);
    });
  }

  return {
    deletionIsLoading,
    deletionPopUpIsOpen,
    confirmAddressDeletion,
    deleteAddress,
    cancelDeletion,
    warning,
  };
}
