import {
  AddressResponseDataType,
  AddressType,
  CityResponseDataType,
} from "@/modules/checkout/types/addresses";

export function castToAddressType(
  addressInResponse: AddressResponseDataType
): AddressType {
  return {
    id: addressInResponse.id,
    firstName: addressInResponse.firstName,
    lastName: addressInResponse.lastName,
    address1: addressInResponse.address1,
    address2: addressInResponse.address2 ? addressInResponse.address2 : "",
    zone: addressInResponse.zone,
    city: {
      id: addressInResponse.city.id,
      name: addressInResponse.city.name,
      country: {
        name: addressInResponse.city.country.name,
        code: addressInResponse.city.country.code,
      },
    },
    postalCode: addressInResponse.postalCode,
    phone: addressInResponse.phone,
    company: addressInResponse.company ? addressInResponse.company : "",
  };
}

export function castToCityType(city: CityResponseDataType) {
  return {
    name: city.name,
    code: city.id,
  };
}
