import { DELETE } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";

type ResponseType = {
  ok: boolean;
  status: number;
  error?: string;
};

export default async function deleteAddressOnServerSide(
  addressId: string
): Promise<ResponseType> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await DELETE(`/addresses/${addressId}`, header);

    return {
      ok: true,
      status: 204,
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        deleteAddressOnServerSide(addressId)
      );

      //unauthorized user error is already handled by the user hook
      if (!res)
        return {
          ok: false,
          status: 401,
          error: "Unauthorized!",
        };
      return res;
    }
    return {
      ok: false,
      status: 500,
      error: "An unexpected error occurred.",
    };
  }
}
