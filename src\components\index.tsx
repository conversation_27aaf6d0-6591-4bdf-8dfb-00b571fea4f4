// Custom
import InputField from './custom/InputField';
import InputFieldBig from './custom/InputFieldBig';
import HomeIndicator from './custom/HomeIndicator';

// Product
import ProductSale from './product/ProductSale';
import ProductCard from './product/ProductCard';
import ProductName from './product/ProductName';
import ProductNameInner from './product-inner/ProductNameInner';
import ProductInWishlist from './product/ProductInWishlist';

// Product inner
import ProductTab from './product-inner/ProductTab';
import ProductSize from './product-inner/ProductSize';
import ProductColor from './product-inner/ProductColor';
import ProductPriceInner from './product-inner/ProductPriceInner';

// Items
import ShopItem from './items/ShopItem';
import OrderItem from './order/OrderItem';
import ProfileItem from './items/ProfileItem';
import CarouselItem from './items/CarouselItem';
import WishlistItem from './items/WishlistItem';
import CategoryItem from './items/CategoryItem';
import NotificationItem from './items/NotificationItem';

// Buttons
import Status from './buttons/Status';
import InCart from './buttons/InCart';
import Button from './buttons/Button';
import ShopNow from './buttons/ShopNow';
import InWishlist from './buttons/InWishlist';

// Order
import OrderItemBtn from './order/OrderItemBtn';
import OrderCounter from './order/OrderCounter';
import OrderContainer from './order/OrderContainer';
import Rating from './Rating';

// Badges
import SaleBadge from './badges/SaleBadge';

// Other
import Line from './Line';
import RatingStars from './RatingStars';
import BlockHeading from './BlockHeading';
import ReviewItem from './items/ReviewItem';
import OrderStatus from './order/OrderStatus';

// Other
import Loader from './Loader';
import Header from './Header';
import Checkbox from './Checkbox';
import TabBar from './tab-bar/TabBar';
import StatusBar from './custom/StatusBar';
import SmartView from './custom/SmartView';
import TabBarItem from './tab-bar/TabBarItem';

// History
import HistoryDataFooter from './history/HistoryDataFooter';
import HistoryDataHeader from './history/HistoryDataHeader';

// Container
import Container from './container/Container';
import ContainerData from './container/ContainerData';
import ContainerItem from './container/ContainerItem';
import ContainerLine from './container/ContainerLine';

// Modal
import BurgerMenuModal from './modal/BurgerMenuModal';
import BurgerMenuItem from './modal/BurgerMenuItem';

// Social
import Facebook from './social/Facebook';
import Google from './social/Google';
import Twitter from './social/Twitter';

export const components = {
  Line,
  Loader,
  TabBar,
  Rating,
  Status,
  Header,
  Button,
  Google,
  InCart,
  ShopNow,
  Twitter,
  Checkbox,
  ShopItem,
  Facebook,
  OrderItem,
  StatusBar,
  SaleBadge,
  Container,
  SmartView,
  InWishlist,
  ProductTab,
  TabBarItem,
  ReviewItem,
  InputField,
  OrderStatus,
  ProductName,
  ProductSize,
  ProductCard,
  ProductSale,
  RatingStars,
  ProfileItem,
  CategoryItem,
  WishlistItem,
  OrderItemBtn,
  OrderCounter,
  OrderContainer,
  ProductColor,
  BlockHeading,
  CarouselItem,
  ContainerLine,
  HomeIndicator,
  ContainerItem,
  InputFieldBig,
  ContainerData,
  BurgerMenuItem,
  BurgerMenuModal,
  ProductNameInner,
  NotificationItem,
  ProductInWishlist,
  HistoryDataHeader,
  ProductPriceInner,
  HistoryDataFooter,
};
