import Text from "@/styles/text-styles";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { ChevronDown, MapPin } from "lucide-react";

interface Props {
  data: { code: string; name: string }[];
  selectedElement: { code: string; name: string };
  onChange: (value: string) => void;
  primaryTheme?: boolean;
}

export default function AddressDropdownMenu({
  data,
  selectedElement,
  onChange,
  primaryTheme = true,
}: Props) {
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const [triggerWidth, setTriggerWidth] = useState<number>(0);

  useEffect(() => {
    if (triggerRef.current) {
      setTriggerWidth(triggerRef.current.offsetWidth);
    }
  }, [menuIsOpen]);

  // useEffect(() => {
  //   if (menuIsOpen) document.body.classList.add("overflow-hidden");
  //   else document.body.classList.remove("overflow-hidden");
  // }, [menuIsOpen]);

  return (
    <DropdownMenu
      open={menuIsOpen}
      onOpenChange={(open) => setMenuIsOpen(open)}
    >
      <DropdownMenuTrigger
        ref={triggerRef}
        className={cn(
          "w-full border py-3 px-4 h-full rounded-[15px] flex items-center justify-between bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1",
          {
            "text-primary border-primary": primaryTheme,
            "text-primary border-gray": !primaryTheme,
          }
        )}
      >
        <div className="flex items-center gap-2">
          <MapPin className="w-4 h-4 text-gray-400" />
          <Text textStyle="TS6" className="text-left">
            {selectedElement.name}
          </Text>
        </div>
        <ChevronDown
          className={cn(
            "w-4 h-4 text-gray-400 transition-transform duration-200",
            {
              "rotate-180": menuIsOpen,
            }
          )}
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className={cn(
          "p-1 bg-white shadow-lg border rounded-[15px] max-h-[300px] overflow-y-auto",
          {
            "border-primary": primaryTheme,
            "border-gray": !primaryTheme,
          }
        )}
        style={{ width: triggerWidth > 0 ? `${triggerWidth}px` : "auto" }}
        sideOffset={4}
      >
        {data.map((element, idx) => (
          <DropdownMenuItem
            key={idx}
            className={cn(
              "w-full px-4 py-3 rounded-[10px] cursor-pointer transition-all duration-200 flex items-center gap-2 hover:bg-gray-50 focus:bg-gray-50 border-none outline-none",
              {
                "text-gray-700 hover:text-primary focus:text-primary":
                  !primaryTheme,
                "text-primary hover:bg-primary/5 focus:bg-primary/5":
                  primaryTheme,
                "bg-primary/10 text-primary font-medium":
                  selectedElement.code === element.code,
              }
            )}
            onClick={() => {
              onChange(element.code);
              setMenuIsOpen(false);
            }}
          >
            <Text
              textStyle="TS7"
              className={cn("flex-1 text-left", {
                "font-medium": selectedElement.code === element.code,
              })}
            >
              {element.name}
            </Text>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
