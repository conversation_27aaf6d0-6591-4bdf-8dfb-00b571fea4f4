"use client";
import useResetPassword from "@auth/hooks/use-reset-password";
import { useContext, createContext, Dispatch, SetStateAction } from "react";

interface ResetPasswordContextType {
  step: "email" | "code" | "password";
  warning: { email: string; generalWarning: string };
  passwordWarning: {
    password: string;
    confirmationPassword: string;
    generalWarning: string;
  };
  submitEmail: () => void;
  displayedTimer: string;
  submitCode: (code: string) => void;
  code: string;
  setCode: Dispatch<SetStateAction<string>>;
  email: string;
  setEmail: Dispatch<SetStateAction<string>>;
  submitPassword: () => void;
  setStep: (step: "email" | "code" | "password") => void;
  startPasswordStep: boolean;
  isLoading: boolean;
}

const ResetPasswordContext = createContext<ResetPasswordContextType>({
  step: "email",
  warning: { email: "", generalWarning: "" },
  passwordWarning: {
    password: "",
    confirmationPassword: "",
    generalWarning: "",
  },
  submitEmail: () => {},
  displayedTimer: "2:00",
  submitCode: () => {},
  code: "",
  setCode: () => {},
  email: "",
  setEmail: () => {},
  submitPassword: () => {},
  setStep: () => {},
  startPasswordStep: false,
  isLoading: false,
});

export function useResetPasswordContext() {
  const resetPassword = useContext(ResetPasswordContext);

  return resetPassword;
}

export function ResetPasswordProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const resetPassword = useResetPassword();

  return (
    <ResetPasswordContext.Provider value={{ ...resetPassword }}>
      {children}
    </ResetPasswordContext.Provider>
  );
}
